{"name": "secure-document-system", "version": "1.0.0", "description": "A full-stack MERN application with MySQL database and local authentication for university curriculum & document portal", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"install:all": "npm install && npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm start", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd backend && npm run lint", "lint:frontend": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["mern", "mysql", "authentication", "rbac", "document-management", "react", "nodejs", "sequelize", "university-portal"], "author": "Your Name", "license": "MIT"}